{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitHub/chess_web/src/components/auth/LoginButton.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { LogIn, LogOut, User } from 'lucide-react'\n\nexport default function LoginButton() {\n  const { user, signInWithGoogle, signOut, loading } = useAuth()\n  const [isLoading, setIsLoading] = useState(false)\n\n  const handleSignIn = async () => {\n    setIsLoading(true)\n    try {\n      await signInWithGoogle()\n    } catch (error) {\n      console.error('Sign in error:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleSignOut = async () => {\n    setIsLoading(true)\n    try {\n      await signOut()\n    } catch (error) {\n      console.error('Sign out error:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"animate-pulse bg-gray-200 rounded-md h-10 w-24\"></div>\n    )\n  }\n\n  if (user) {\n    return (\n      <div className=\"flex items-center space-x-4\">\n        <div className=\"flex items-center space-x-2\">\n          {user.avatar_url ? (\n            <img\n              src={user.avatar_url}\n              alt={user.display_name}\n              className=\"w-8 h-8 rounded-full\"\n            />\n          ) : (\n            <div className=\"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center\">\n              <User className=\"w-4 h-4 text-gray-600\" />\n            </div>\n          )}\n          <span className=\"text-sm font-medium text-gray-700\">\n            {user.display_name}\n          </span>\n        </div>\n        <button\n          onClick={handleSignOut}\n          disabled={isLoading}\n          className=\"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n        >\n          {isLoading ? (\n            <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\n          ) : (\n            <>\n              <LogOut className=\"w-4 h-4 mr-1\" />\n              Sign Out\n            </>\n          )}\n        </button>\n      </div>\n    )\n  }\n\n  return (\n    <button\n      onClick={handleSignIn}\n      disabled={isLoading}\n      className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n    >\n      {isLoading ? (\n        <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\n      ) : (\n        <>\n          <LogIn className=\"w-4 h-4 mr-2\" />\n          Sign in with Google\n        </>\n      )}\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,gBAAgB,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC3D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,eAAe;QACnB,aAAa;QACb,IAAI;YACF,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;QAClC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB;QACpB,aAAa;QACb,IAAI;YACF,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;QACnC,SAAU;YACR,aAAa;QACf;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;;;;;;IAEnB;IAEA,IAAI,MAAM;QACR,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;wBACZ,KAAK,UAAU,iBACd,8OAAC;4BACC,KAAK,KAAK,UAAU;4BACpB,KAAK,KAAK,YAAY;4BACtB,WAAU;;;;;iDAGZ,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;sCAGpB,8OAAC;4BAAK,WAAU;sCACb,KAAK,YAAY;;;;;;;;;;;;8BAGtB,8OAAC;oBACC,SAAS;oBACT,UAAU;oBACV,WAAU;8BAET,0BACC,8OAAC;wBAAI,WAAU;;;;;6CAEf;;0CACE,8OAAC,0MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;;IAO/C;IAEA,qBACE,8OAAC;QACC,SAAS;QACT,UAAU;QACV,WAAU;kBAET,0BACC,8OAAC;YAAI,WAAU;;;;;iCAEf;;8BACE,8OAAC,wMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;gBAAiB;;;;;;;;AAM5C", "debugId": null}}, {"offset": {"line": 188, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitHub/chess_web/src/components/layout/Navigation.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { useAuth } from '@/contexts/AuthContext'\nimport LoginButton from '@/components/auth/LoginButton'\nimport { Crown, Home, User, Gamepad2 } from 'lucide-react'\n\nexport default function Navigation() {\n  const { user } = useAuth()\n\n  return (\n    <nav className=\"bg-white shadow-lg\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <Crown className=\"h-8 w-8 text-blue-600\" />\n              <span className=\"text-xl font-bold text-gray-900\">ChessHub</span>\n            </Link>\n          </div>\n\n          <div className=\"flex items-center space-x-8\">\n            <Link\n              href=\"/\"\n              className=\"flex items-center space-x-1 text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium\"\n            >\n              <Home className=\"w-4 h-4\" />\n              <span>Home</span>\n            </Link>\n\n            <Link\n              href=\"/demo\"\n              className=\"flex items-center space-x-1 text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium\"\n            >\n              <Gamepad2 className=\"w-4 h-4\" />\n              <span>Demo</span>\n            </Link>\n\n            {user && (\n              <>\n                <Link\n                  href=\"/play\"\n                  className=\"flex items-center space-x-1 text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  <Gamepad2 className=\"w-4 h-4\" />\n                  <span>Play</span>\n                </Link>\n                <Link\n                  href=\"/profile\"\n                  className=\"flex items-center space-x-1 text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  <User className=\"w-4 h-4\" />\n                  <span>Profile</span>\n                </Link>\n              </>\n            )}\n\n            <LoginButton />\n          </div>\n        </div>\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEvB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC;oCAAK,WAAU;8CAAkC;;;;;;;;;;;;;;;;;kCAItD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC,mMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;kDAAK;;;;;;;;;;;;0CAGR,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;kDAAK;;;;;;;;;;;;4BAGP,sBACC;;kDACE,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;0DAAK;;;;;;;;;;;;;;0CAKZ,8OAAC,yIAAA,CAAA,UAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMxB", "debugId": null}}, {"offset": {"line": 383, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitHub/chess_web/src/hooks/useChessGame.ts"], "sourcesContent": ["'use client'\n\nimport { useState, useCallback, useEffect } from 'react'\nimport { Chess, Square } from 'chess.js'\n\nexport interface ChessGameState {\n  game: Chess\n  gameId?: string\n  playerColor: 'white' | 'black'\n  isPlayerTurn: boolean\n  gameStatus: 'waiting' | 'active' | 'completed' | 'abandoned'\n  winner?: 'white' | 'black' | 'draw'\n  moveHistory: string[]\n}\n\nexport function useChessGame(initialFen?: string) {\n  const [gameState, setGameState] = useState<ChessGameState>(() => ({\n    game: new Chess(initialFen),\n    playerColor: 'white',\n    isPlayerTurn: true,\n    gameStatus: 'active', // Start as active for demo mode\n    moveHistory: []\n  }))\n\n  const [selectedSquare, setSelectedSquare] = useState<Square | null>(null)\n  const [possibleMoves, setPossibleMoves] = useState<Square[]>([])\n\n  // Get possible moves for a square\n  const getPossibleMoves = useCallback((square: Square): Square[] => {\n    const moves = gameState.game.moves({ square, verbose: true })\n    return moves.map(move => move.to as Square)\n  }, [gameState.game])\n\n  // Handle square click\n  const onSquareClick = useCallback((square: Square) => {\n    // If no square is selected, select this square if it has a piece\n    if (!selectedSquare) {\n      const piece = gameState.game.get(square)\n      // For demo mode, allow selecting any piece (both colors)\n      if (piece) {\n        setSelectedSquare(square)\n        setPossibleMoves(getPossibleMoves(square))\n      }\n      return\n    }\n\n    // If the same square is clicked, deselect\n    if (selectedSquare === square) {\n      setSelectedSquare(null)\n      setPossibleMoves([])\n      return\n    }\n\n    // Try to make a move\n    const moveResult = makeMove(selectedSquare, square)\n\n    // Clear selection regardless of move success\n    setSelectedSquare(null)\n    setPossibleMoves([])\n\n    return moveResult\n  }, [selectedSquare, gameState, getPossibleMoves])\n\n  // Make a move\n  const makeMove = useCallback((from: Square, to: Square, promotion?: string) => {\n    // For demo mode, always allow moves (skip turn check)\n    // In multiplayer mode, this would check gameState.isPlayerTurn\n\n    try {\n      const move = gameState.game.move({\n        from,\n        to,\n        promotion: promotion || 'q' // Default to queen promotion\n      })\n\n      if (move) {\n        setGameState(prev => ({\n          ...prev,\n          game: new Chess(prev.game.fen()),\n          moveHistory: [...prev.moveHistory, move.san],\n          isPlayerTurn: true // Keep as true for demo mode to allow continuous play\n        }))\n\n        return { success: true, move }\n      } else {\n        return { success: false, error: 'Invalid move' }\n      }\n    } catch (error) {\n      return { success: false, error: 'Invalid move' }\n    }\n  }, [gameState])\n\n  // Handle piece drop (drag and drop)\n  const onPieceDrop = useCallback((sourceSquare: Square, targetSquare: Square) => {\n    const result = makeMove(sourceSquare, targetSquare)\n    return result?.success || false\n  }, [makeMove])\n\n  // Reset game\n  const resetGame = useCallback(() => {\n    setGameState(prev => ({\n      ...prev,\n      game: new Chess(),\n      moveHistory: [],\n      gameStatus: 'active', // Set to active for demo mode\n      winner: undefined,\n      isPlayerTurn: true\n    }))\n    setSelectedSquare(null)\n    setPossibleMoves([])\n  }, [])\n\n  // Update game from external state (for multiplayer)\n  const updateGameState = useCallback((newState: Partial<ChessGameState>) => {\n    setGameState(prev => ({\n      ...prev,\n      ...newState,\n      game: newState.game ? new Chess(newState.game.fen()) : prev.game\n    }))\n  }, [])\n\n  // Check game status\n  useEffect(() => {\n    const game = gameState.game\n    let status: ChessGameState['gameStatus'] = 'active'\n    let winner: ChessGameState['winner'] = undefined\n\n    if (game.isGameOver()) {\n      status = 'completed'\n      if (game.isCheckmate()) {\n        winner = game.turn() === 'w' ? 'black' : 'white'\n      } else if (game.isDraw()) {\n        winner = 'draw'\n      }\n    }\n\n    if (status !== gameState.gameStatus || winner !== gameState.winner) {\n      setGameState(prev => ({\n        ...prev,\n        gameStatus: status,\n        winner\n      }))\n    }\n  }, [gameState.game, gameState.gameStatus, gameState.winner])\n\n  return {\n    gameState,\n    selectedSquare,\n    possibleMoves,\n    onSquareClick,\n    onPieceDrop,\n    makeMove,\n    resetGame,\n    updateGameState,\n    isCheck: gameState.game.isCheck(),\n    isCheckmate: gameState.game.isCheckmate(),\n    isDraw: gameState.game.isDraw(),\n    isGameOver: gameState.game.isGameOver(),\n    currentFen: gameState.game.fen(),\n    pgn: gameState.game.pgn()\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;AAeO,SAAS,aAAa,UAAmB;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,IAAM,CAAC;YAChE,MAAM,IAAI,mJAAA,CAAA,QAAK,CAAC;YAChB,aAAa;YACb,cAAc;YACd,YAAY;YACZ,aAAa,EAAE;QACjB,CAAC;IAED,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAE/D,kCAAkC;IAClC,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,MAAM,QAAQ,UAAU,IAAI,CAAC,KAAK,CAAC;YAAE;YAAQ,SAAS;QAAK;QAC3D,OAAO,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,EAAE;IAClC,GAAG;QAAC,UAAU,IAAI;KAAC;IAEnB,sBAAsB;IACtB,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACjC,iEAAiE;QACjE,IAAI,CAAC,gBAAgB;YACnB,MAAM,QAAQ,UAAU,IAAI,CAAC,GAAG,CAAC;YACjC,yDAAyD;YACzD,IAAI,OAAO;gBACT,kBAAkB;gBAClB,iBAAiB,iBAAiB;YACpC;YACA;QACF;QAEA,0CAA0C;QAC1C,IAAI,mBAAmB,QAAQ;YAC7B,kBAAkB;YAClB,iBAAiB,EAAE;YACnB;QACF;QAEA,qBAAqB;QACrB,MAAM,aAAa,SAAS,gBAAgB;QAE5C,6CAA6C;QAC7C,kBAAkB;QAClB,iBAAiB,EAAE;QAEnB,OAAO;IACT,GAAG;QAAC;QAAgB;QAAW;KAAiB;IAEhD,cAAc;IACd,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,MAAc,IAAY;QACtD,sDAAsD;QACtD,+DAA+D;QAE/D,IAAI;YACF,MAAM,OAAO,UAAU,IAAI,CAAC,IAAI,CAAC;gBAC/B;gBACA;gBACA,WAAW,aAAa,IAAI,6BAA6B;YAC3D;YAEA,IAAI,MAAM;gBACR,aAAa,CAAA,OAAQ,CAAC;wBACpB,GAAG,IAAI;wBACP,MAAM,IAAI,mJAAA,CAAA,QAAK,CAAC,KAAK,IAAI,CAAC,GAAG;wBAC7B,aAAa;+BAAI,KAAK,WAAW;4BAAE,KAAK,GAAG;yBAAC;wBAC5C,cAAc,KAAK,sDAAsD;oBAC3E,CAAC;gBAED,OAAO;oBAAE,SAAS;oBAAM;gBAAK;YAC/B,OAAO;gBACL,OAAO;oBAAE,SAAS;oBAAO,OAAO;gBAAe;YACjD;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAe;QACjD;IACF,GAAG;QAAC;KAAU;IAEd,oCAAoC;IACpC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,cAAsB;QACrD,MAAM,SAAS,SAAS,cAAc;QACtC,OAAO,QAAQ,WAAW;IAC5B,GAAG;QAAC;KAAS;IAEb,aAAa;IACb,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC5B,aAAa,CAAA,OAAQ,CAAC;gBACpB,GAAG,IAAI;gBACP,MAAM,IAAI,mJAAA,CAAA,QAAK;gBACf,aAAa,EAAE;gBACf,YAAY;gBACZ,QAAQ;gBACR,cAAc;YAChB,CAAC;QACD,kBAAkB;QAClB,iBAAiB,EAAE;IACrB,GAAG,EAAE;IAEL,oDAAoD;IACpD,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,aAAa,CAAA,OAAQ,CAAC;gBACpB,GAAG,IAAI;gBACP,GAAG,QAAQ;gBACX,MAAM,SAAS,IAAI,GAAG,IAAI,mJAAA,CAAA,QAAK,CAAC,SAAS,IAAI,CAAC,GAAG,MAAM,KAAK,IAAI;YAClE,CAAC;IACH,GAAG,EAAE;IAEL,oBAAoB;IACpB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,OAAO,UAAU,IAAI;QAC3B,IAAI,SAAuC;QAC3C,IAAI,SAAmC;QAEvC,IAAI,KAAK,UAAU,IAAI;YACrB,SAAS;YACT,IAAI,KAAK,WAAW,IAAI;gBACtB,SAAS,KAAK,IAAI,OAAO,MAAM,UAAU;YAC3C,OAAO,IAAI,KAAK,MAAM,IAAI;gBACxB,SAAS;YACX;QACF;QAEA,IAAI,WAAW,UAAU,UAAU,IAAI,WAAW,UAAU,MAAM,EAAE;YAClE,aAAa,CAAA,OAAQ,CAAC;oBACpB,GAAG,IAAI;oBACP,YAAY;oBACZ;gBACF,CAAC;QACH;IACF,GAAG;QAAC,UAAU,IAAI;QAAE,UAAU,UAAU;QAAE,UAAU,MAAM;KAAC;IAE3D,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,SAAS,UAAU,IAAI,CAAC,OAAO;QAC/B,aAAa,UAAU,IAAI,CAAC,WAAW;QACvC,QAAQ,UAAU,IAAI,CAAC,MAAM;QAC7B,YAAY,UAAU,IAAI,CAAC,UAAU;QACrC,YAAY,UAAU,IAAI,CAAC,GAAG;QAC9B,KAAK,UAAU,IAAI,CAAC,GAAG;IACzB;AACF", "debugId": null}}, {"offset": {"line": 555, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitHub/chess_web/src/components/chess/ChessBoard.tsx"], "sourcesContent": ["'use client'\n\nimport { Chessboard } from 'react-chessboard'\nimport { Square } from 'chess.js'\nimport { useChessGame } from '@/hooks/useChessGame'\nimport { Crown, RotateCcw, Flag } from 'lucide-react'\n\ninterface ChessBoardProps {\n  gameId?: string\n  playerColor?: 'white' | 'black'\n  isSpectator?: boolean\n  onMove?: (move: any) => void\n  initialFen?: string\n}\n\nexport default function ChessBoard({ \n  gameId, \n  playerColor = 'white', \n  isSpectator = false,\n  onMove,\n  initialFen \n}: ChessBoardProps) {\n  const {\n    gameState,\n    selectedSquare,\n    possibleMoves,\n    onSquareClick,\n    onPieceDrop,\n    resetGame,\n    isCheck,\n    isCheckmate,\n    isDraw,\n    isGameOver,\n    currentFen,\n    pgn\n  } = useChessGame(initialFen)\n\n  // Custom square styles for highlighting\n  const customSquareStyles = () => {\n    const styles: { [square: string]: React.CSSProperties } = {}\n\n    // Highlight selected square\n    if (selectedSquare) {\n      styles[selectedSquare] = {\n        backgroundColor: 'rgba(255, 255, 0, 0.4)'\n      }\n    }\n\n    // Highlight possible moves\n    possibleMoves.forEach(square => {\n      styles[square] = {\n        background: 'radial-gradient(circle, rgba(0,0,0,.1) 25%, transparent 25%)',\n        borderRadius: '50%'\n      }\n    })\n\n    // Highlight check\n    if (isCheck) {\n      const kingSquare = gameState.game.board().flat().find(\n        piece => piece && piece.type === 'k' && piece.color === gameState.game.turn()\n      )\n      if (kingSquare) {\n        // Find king position - this is a simplified approach\n        for (let rank = 0; rank < 8; rank++) {\n          for (let file = 0; file < 8; file++) {\n            const square = String.fromCharCode(97 + file) + (8 - rank) as Square\n            const piece = gameState.game.get(square)\n            if (piece && piece.type === 'k' && piece.color === gameState.game.turn()) {\n              styles[square] = {\n                backgroundColor: 'rgba(255, 0, 0, 0.4)'\n              }\n            }\n          }\n        }\n      }\n    }\n\n    return styles\n  }\n\n  const handlePieceDrop = (sourceSquare: Square, targetSquare: Square) => {\n    if (isSpectator) return false\n    \n    const result = onPieceDrop(sourceSquare, targetSquare)\n    \n    if (result && onMove) {\n      // Get the last move from the game\n      const history = gameState.game.history({ verbose: true })\n      const lastMove = history[history.length - 1]\n      onMove(lastMove)\n    }\n    \n    return result\n  }\n\n  const getGameStatusMessage = () => {\n    if (isCheckmate) {\n      const winner = gameState.game.turn() === 'w' ? 'Black' : 'White'\n      return `Checkmate! ${winner} wins!`\n    }\n    if (isDraw) {\n      return 'Game ended in a draw'\n    }\n    if (isCheck) {\n      return 'Check!'\n    }\n    if (gameState.gameStatus === 'waiting') {\n      return 'Waiting for opponent...'\n    }\n    return `${gameState.game.turn() === 'w' ? 'White' : 'Black'} to move`\n  }\n\n  return (\n    <div className=\"flex flex-col items-center space-y-4\">\n      {/* Game Status */}\n      <div className=\"bg-white rounded-lg shadow-md p-4 w-full max-w-md text-center\">\n        <div className=\"flex items-center justify-center space-x-2 mb-2\">\n          <Crown className=\"h-5 w-5 text-yellow-500\" />\n          <span className=\"font-semibold text-gray-800\">\n            {getGameStatusMessage()}\n          </span>\n        </div>\n        \n        {gameState.gameStatus === 'active' && (\n          <div className=\"text-sm text-gray-600\">\n            Turn: {gameState.game.turn() === 'w' ? 'White' : 'Black'}\n          </div>\n        )}\n      </div>\n\n      {/* Chess Board */}\n      <div className=\"relative\">\n        <Chessboard\n          position={currentFen}\n          onPieceDrop={handlePieceDrop}\n          onSquareClick={!isSpectator ? onSquareClick : undefined}\n          boardOrientation={playerColor}\n          customSquareStyles={customSquareStyles()}\n          areArrowsAllowed={true}\n          boardWidth={400}\n          customBoardStyle={{\n            borderRadius: '8px',\n            boxShadow: '0 10px 25px rgba(0, 0, 0, 0.2)'\n          }}\n        />\n        \n        {/* Game Over Overlay */}\n        {isGameOver && (\n          <div className=\"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center rounded-lg\">\n            <div className=\"bg-white p-6 rounded-lg text-center\">\n              <h3 className=\"text-xl font-bold mb-2\">Game Over</h3>\n              <p className=\"text-gray-600 mb-4\">{getGameStatusMessage()}</p>\n              {!isSpectator && (\n                <button\n                  onClick={resetGame}\n                  className=\"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700\"\n                >\n                  <RotateCcw className=\"h-4 w-4 mr-2\" />\n                  New Game\n                </button>\n              )}\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Game Controls */}\n      {!isSpectator && gameState.gameStatus === 'active' && (\n        <div className=\"flex space-x-2\">\n          <button\n            onClick={resetGame}\n            className=\"inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50\"\n          >\n            <RotateCcw className=\"h-4 w-4 mr-1\" />\n            Reset\n          </button>\n          \n          <button\n            className=\"inline-flex items-center px-3 py-2 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-white hover:bg-red-50\"\n          >\n            <Flag className=\"h-4 w-4 mr-1\" />\n            Resign\n          </button>\n        </div>\n      )}\n\n      {/* Move History */}\n      <div className=\"bg-white rounded-lg shadow-md p-4 w-full max-w-md\">\n        <h3 className=\"font-semibold text-gray-800 mb-2\">Move History</h3>\n        <div className=\"max-h-32 overflow-y-auto text-sm\">\n          {gameState.moveHistory.length > 0 ? (\n            <div className=\"grid grid-cols-2 gap-2\">\n              {gameState.moveHistory.map((move, index) => (\n                <div key={index} className=\"text-gray-600\">\n                  {Math.floor(index / 2) + 1}.{index % 2 === 0 ? '' : '..'} {move}\n                </div>\n              ))}\n            </div>\n          ) : (\n            <p className=\"text-gray-500\">No moves yet</p>\n          )}\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AAAA;AAAA;AALA;;;;;AAee,SAAS,WAAW,EACjC,MAAM,EACN,cAAc,OAAO,EACrB,cAAc,KAAK,EACnB,MAAM,EACN,UAAU,EACM;IAChB,MAAM,EACJ,SAAS,EACT,cAAc,EACd,aAAa,EACb,aAAa,EACb,WAAW,EACX,SAAS,EACT,OAAO,EACP,WAAW,EACX,MAAM,EACN,UAAU,EACV,UAAU,EACV,GAAG,EACJ,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD,EAAE;IAEjB,wCAAwC;IACxC,MAAM,qBAAqB;QACzB,MAAM,SAAoD,CAAC;QAE3D,4BAA4B;QAC5B,IAAI,gBAAgB;YAClB,MAAM,CAAC,eAAe,GAAG;gBACvB,iBAAiB;YACnB;QACF;QAEA,2BAA2B;QAC3B,cAAc,OAAO,CAAC,CAAA;YACpB,MAAM,CAAC,OAAO,GAAG;gBACf,YAAY;gBACZ,cAAc;YAChB;QACF;QAEA,kBAAkB;QAClB,IAAI,SAAS;YACX,MAAM,aAAa,UAAU,IAAI,CAAC,KAAK,GAAG,IAAI,GAAG,IAAI,CACnD,CAAA,QAAS,SAAS,MAAM,IAAI,KAAK,OAAO,MAAM,KAAK,KAAK,UAAU,IAAI,CAAC,IAAI;YAE7E,IAAI,YAAY;gBACd,qDAAqD;gBACrD,IAAK,IAAI,OAAO,GAAG,OAAO,GAAG,OAAQ;oBACnC,IAAK,IAAI,OAAO,GAAG,OAAO,GAAG,OAAQ;wBACnC,MAAM,SAAS,OAAO,YAAY,CAAC,KAAK,QAAQ,CAAC,IAAI,IAAI;wBACzD,MAAM,QAAQ,UAAU,IAAI,CAAC,GAAG,CAAC;wBACjC,IAAI,SAAS,MAAM,IAAI,KAAK,OAAO,MAAM,KAAK,KAAK,UAAU,IAAI,CAAC,IAAI,IAAI;4BACxE,MAAM,CAAC,OAAO,GAAG;gCACf,iBAAiB;4BACnB;wBACF;oBACF;gBACF;YACF;QACF;QAEA,OAAO;IACT;IAEA,MAAM,kBAAkB,CAAC,cAAsB;QAC7C,IAAI,aAAa,OAAO;QAExB,MAAM,SAAS,YAAY,cAAc;QAEzC,IAAI,UAAU,QAAQ;YACpB,kCAAkC;YAClC,MAAM,UAAU,UAAU,IAAI,CAAC,OAAO,CAAC;gBAAE,SAAS;YAAK;YACvD,MAAM,WAAW,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE;YAC5C,OAAO;QACT;QAEA,OAAO;IACT;IAEA,MAAM,uBAAuB;QAC3B,IAAI,aAAa;YACf,MAAM,SAAS,UAAU,IAAI,CAAC,IAAI,OAAO,MAAM,UAAU;YACzD,OAAO,CAAC,WAAW,EAAE,OAAO,MAAM,CAAC;QACrC;QACA,IAAI,QAAQ;YACV,OAAO;QACT;QACA,IAAI,SAAS;YACX,OAAO;QACT;QACA,IAAI,UAAU,UAAU,KAAK,WAAW;YACtC,OAAO;QACT;QACA,OAAO,GAAG,UAAU,IAAI,CAAC,IAAI,OAAO,MAAM,UAAU,QAAQ,QAAQ,CAAC;IACvE;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;gCAAK,WAAU;0CACb;;;;;;;;;;;;oBAIJ,UAAU,UAAU,KAAK,0BACxB,8OAAC;wBAAI,WAAU;;4BAAwB;4BAC9B,UAAU,IAAI,CAAC,IAAI,OAAO,MAAM,UAAU;;;;;;;;;;;;;0BAMvD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,2JAAA,CAAA,aAAU;wBACT,UAAU;wBACV,aAAa;wBACb,eAAe,CAAC,cAAc,gBAAgB;wBAC9C,kBAAkB;wBAClB,oBAAoB;wBACpB,kBAAkB;wBAClB,YAAY;wBACZ,kBAAkB;4BAChB,cAAc;4BACd,WAAW;wBACb;;;;;;oBAID,4BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAyB;;;;;;8CACvC,8OAAC;oCAAE,WAAU;8CAAsB;;;;;;gCAClC,CAAC,6BACA,8OAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;YAUjD,CAAC,eAAe,UAAU,UAAU,KAAK,0BACxC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,SAAS;wBACT,WAAU;;0CAEV,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;kCAIxC,8OAAC;wBACC,WAAU;;0CAEV,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAOvC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,8OAAC;wBAAI,WAAU;kCACZ,UAAU,WAAW,CAAC,MAAM,GAAG,kBAC9B,8OAAC;4BAAI,WAAU;sCACZ,UAAU,WAAW,CAAC,GAAG,CAAC,CAAC,MAAM,sBAChC,8OAAC;oCAAgB,WAAU;;wCACxB,KAAK,KAAK,CAAC,QAAQ,KAAK;wCAAE;wCAAE,QAAQ,MAAM,IAAI,KAAK;wCAAK;wCAAE;;mCADnD;;;;;;;;;iDAMd,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;;;;;;;AAMzC", "debugId": null}}, {"offset": {"line": 868, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitHub/chess_web/src/app/demo/page.tsx"], "sourcesContent": ["'use client'\n\nimport Navigation from '@/components/layout/Navigation'\nimport ChessBoard from '@/components/chess/ChessBoard'\nimport { ArrowLeft } from 'lucide-react'\nimport Link from 'next/link'\n\nexport default function DemoPage() {\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Navigation />\n      \n      <div className=\"max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8\">\n        <div className=\"mb-8\">\n          <Link\n            href=\"/\"\n            className=\"inline-flex items-center text-sm text-gray-500 hover:text-gray-700 mb-4\"\n          >\n            <ArrowLeft className=\"h-4 w-4 mr-1\" />\n            Back to Home\n          </Link>\n          <h1 className=\"text-3xl font-extrabold text-gray-900 mb-2\">\n            Chess Demo\n          </h1>\n          <p className=\"text-lg text-gray-600\">\n            Try out the chess board! You can play against yourself to test the functionality.\n          </p>\n        </div>\n\n        <div className=\"flex justify-center\">\n          <ChessBoard \n            playerColor=\"white\"\n            isSpectator={false}\n          />\n        </div>\n\n        <div className=\"mt-8 max-w-2xl mx-auto\">\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">How to Play</h2>\n            <ul className=\"space-y-2 text-gray-600\">\n              <li>• Click on a piece to select it</li>\n              <li>• Click on a highlighted square to move the piece</li>\n              <li>• You can also drag and drop pieces</li>\n              <li>• The game follows standard chess rules</li>\n              <li>• Move history is shown below the board</li>\n            </ul>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,0IAAA,CAAA,UAAU;;;;;0BAEX,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGxC,8OAAC;gCAAG,WAAU;0CAA6C;;;;;;0CAG3D,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAKvC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,yIAAA,CAAA,UAAU;4BACT,aAAY;4BACZ,aAAa;;;;;;;;;;;kCAIjB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CACzD,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlB", "debugId": null}}]}