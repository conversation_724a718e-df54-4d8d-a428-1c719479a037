{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitHub/chess_web/src/components/auth/LoginButton.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { LogIn, LogOut, User } from 'lucide-react'\n\nexport default function LoginButton() {\n  const { user, signInWithGoogle, signOut, loading } = useAuth()\n  const [isLoading, setIsLoading] = useState(false)\n\n  const handleSignIn = async () => {\n    setIsLoading(true)\n    try {\n      await signInWithGoogle()\n    } catch (error) {\n      console.error('Sign in error:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleSignOut = async () => {\n    setIsLoading(true)\n    try {\n      await signOut()\n    } catch (error) {\n      console.error('Sign out error:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"animate-pulse bg-gray-200 rounded-md h-10 w-24\"></div>\n    )\n  }\n\n  if (user) {\n    return (\n      <div className=\"flex items-center space-x-4\">\n        <div className=\"flex items-center space-x-2\">\n          {user.avatar_url ? (\n            <img\n              src={user.avatar_url}\n              alt={user.display_name}\n              className=\"w-8 h-8 rounded-full\"\n            />\n          ) : (\n            <div className=\"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center\">\n              <User className=\"w-4 h-4 text-gray-600\" />\n            </div>\n          )}\n          <span className=\"text-sm font-medium text-gray-700\">\n            {user.display_name}\n          </span>\n        </div>\n        <button\n          onClick={handleSignOut}\n          disabled={isLoading}\n          className=\"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n        >\n          {isLoading ? (\n            <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\n          ) : (\n            <>\n              <LogOut className=\"w-4 h-4 mr-1\" />\n              Sign Out\n            </>\n          )}\n        </button>\n      </div>\n    )\n  }\n\n  return (\n    <button\n      onClick={handleSignIn}\n      disabled={isLoading}\n      className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n    >\n      {isLoading ? (\n        <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\n      ) : (\n        <>\n          <LogIn className=\"w-4 h-4 mr-2\" />\n          Sign in with Google\n        </>\n      )}\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,gBAAgB,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC3D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,eAAe;QACnB,aAAa;QACb,IAAI;YACF,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;QAClC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB;QACpB,aAAa;QACb,IAAI;YACF,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;QACnC,SAAU;YACR,aAAa;QACf;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;;;;;;IAEnB;IAEA,IAAI,MAAM;QACR,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;wBACZ,KAAK,UAAU,iBACd,6LAAC;4BACC,KAAK,KAAK,UAAU;4BACpB,KAAK,KAAK,YAAY;4BACtB,WAAU;;;;;iDAGZ,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;sCAGpB,6LAAC;4BAAK,WAAU;sCACb,KAAK,YAAY;;;;;;;;;;;;8BAGtB,6LAAC;oBACC,SAAS;oBACT,UAAU;oBACV,WAAU;8BAET,0BACC,6LAAC;wBAAI,WAAU;;;;;6CAEf;;0CACE,6LAAC,6MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;;IAO/C;IAEA,qBACE,6LAAC;QACC,SAAS;QACT,UAAU;QACV,WAAU;kBAET,0BACC,6LAAC;YAAI,WAAU;;;;;iCAEf;;8BACE,6LAAC,2MAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;gBAAiB;;;;;;;;AAM5C;GArFwB;;QAC+B,kIAAA,CAAA,UAAO;;;KADtC", "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitHub/chess_web/src/components/layout/Navigation.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { useAuth } from '@/contexts/AuthContext'\nimport LoginButton from '@/components/auth/LoginButton'\nimport { Crown, Home, User, Gamepad2 } from 'lucide-react'\n\nexport default function Navigation() {\n  const { user } = useAuth()\n\n  return (\n    <nav className=\"bg-white shadow-lg\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <Crown className=\"h-8 w-8 text-blue-600\" />\n              <span className=\"text-xl font-bold text-gray-900\">ChessHub</span>\n            </Link>\n          </div>\n\n          <div className=\"flex items-center space-x-8\">\n            <Link\n              href=\"/\"\n              className=\"flex items-center space-x-1 text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium\"\n            >\n              <Home className=\"w-4 h-4\" />\n              <span>Home</span>\n            </Link>\n\n            <Link\n              href=\"/demo\"\n              className=\"flex items-center space-x-1 text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium\"\n            >\n              <Gamepad2 className=\"w-4 h-4\" />\n              <span>Demo</span>\n            </Link>\n\n            {user && (\n              <>\n                <Link\n                  href=\"/play\"\n                  className=\"flex items-center space-x-1 text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  <Gamepad2 className=\"w-4 h-4\" />\n                  <span>Play</span>\n                </Link>\n                <Link\n                  href=\"/profile\"\n                  className=\"flex items-center space-x-1 text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  <User className=\"w-4 h-4\" />\n                  <span>Profile</span>\n                </Link>\n              </>\n            )}\n\n            <LoginButton />\n          </div>\n        </div>\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEvB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,6LAAC;oCAAK,WAAU;8CAAkC;;;;;;;;;;;;;;;;;kCAItD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+J<PERSON><PERSON>,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,6LAAC,sMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6LAAC;kDAAK;;;;;;;;;;;;0CAGR,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;kDAAK;;;;;;;;;;;;4BAGP,sBACC;;kDACE,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;0DAAK;;;;;;;;;;;;;;0CAKZ,6LAAC,4IAAA,CAAA,UAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMxB;GAxDwB;;QACL,kIAAA,CAAA,UAAO;;;KADF", "debugId": null}}, {"offset": {"line": 385, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitHub/chess_web/src/hooks/useChessGame.ts"], "sourcesContent": ["'use client'\n\nimport { useState, useCallback, useEffect } from 'react'\nimport { Chess, Square } from 'chess.js'\n\nexport interface ChessGameState {\n  game: Chess\n  gameId?: string\n  playerColor: 'white' | 'black'\n  isPlayerTurn: boolean\n  gameStatus: 'waiting' | 'active' | 'completed' | 'abandoned'\n  winner?: 'white' | 'black' | 'draw'\n  moveHistory: string[]\n}\n\nexport function useChessGame(initialFen?: string) {\n  const [gameState, setGameState] = useState<ChessGameState>(() => ({\n    game: new Chess(initialFen),\n    playerColor: 'white',\n    isPlayerTurn: true,\n    gameStatus: 'active', // Start as active for demo mode\n    moveHistory: []\n  }))\n\n  const [selectedSquare, setSelectedSquare] = useState<Square | null>(null)\n  const [possibleMoves, setPossibleMoves] = useState<Square[]>([])\n\n  // Get possible moves for a square\n  const getPossibleMoves = useCallback((square: Square): Square[] => {\n    const moves = gameState.game.moves({ square, verbose: true })\n    return moves.map(move => move.to as Square)\n  }, [gameState.game])\n\n  // Make a move\n  const makeMove = useCallback((from: Square, to: Square, promotion?: string) => {\n    // For demo mode, always allow moves (skip turn check)\n    // In multiplayer mode, this would check gameState.isPlayerTurn\n\n    try {\n      // Create a copy of the game to test the move\n      const gameCopy = new Chess(gameState.game.fen())\n      const move = gameCopy.move({\n        from,\n        to,\n        promotion: promotion || 'q' // Default to queen promotion\n      })\n\n      if (move) {\n        // Update state with the new game state\n        setGameState(prev => ({\n          ...prev,\n          game: gameCopy, // Use the updated game copy\n          moveHistory: [...prev.moveHistory, move.san],\n          isPlayerTurn: true // Keep as true for demo mode to allow continuous play\n        }))\n\n        return { success: true, move }\n      } else {\n        return { success: false, error: 'Invalid move' }\n      }\n    } catch (error) {\n      return { success: false, error: 'Invalid move' }\n    }\n  }, [gameState.game])\n\n  // Handle square click\n  const onSquareClick = useCallback((square: Square) => {\n    // If no square is selected, select this square if it has a piece\n    if (!selectedSquare) {\n      const piece = gameState.game.get(square)\n      // For demo mode, allow selecting any piece (both colors)\n      if (piece) {\n        setSelectedSquare(square)\n        setPossibleMoves(getPossibleMoves(square))\n      }\n      return\n    }\n\n    // If the same square is clicked, deselect\n    if (selectedSquare === square) {\n      setSelectedSquare(null)\n      setPossibleMoves([])\n      return\n    }\n\n    // Try to make a move\n    const moveResult = makeMove(selectedSquare, square)\n\n    // Clear selection regardless of move success\n    setSelectedSquare(null)\n    setPossibleMoves([])\n\n    return moveResult\n  }, [selectedSquare, gameState.game, getPossibleMoves, makeMove])\n\n  // Handle piece drop (drag and drop)\n  const onPieceDrop = useCallback((sourceSquare: Square, targetSquare: Square) => {\n    const result = makeMove(sourceSquare, targetSquare)\n    return result?.success || false\n  }, [makeMove])\n\n  // Reset game\n  const resetGame = useCallback(() => {\n    setGameState(prev => ({\n      ...prev,\n      game: new Chess(),\n      moveHistory: [],\n      gameStatus: 'active', // Set to active for demo mode\n      winner: undefined,\n      isPlayerTurn: true\n    }))\n    setSelectedSquare(null)\n    setPossibleMoves([])\n  }, [])\n\n  // Update game from external state (for multiplayer)\n  const updateGameState = useCallback((newState: Partial<ChessGameState>) => {\n    setGameState(prev => ({\n      ...prev,\n      ...newState,\n      game: newState.game ? new Chess(newState.game.fen()) : prev.game\n    }))\n  }, [])\n\n  // Check game status\n  useEffect(() => {\n    const game = gameState.game\n    let status: ChessGameState['gameStatus'] = 'active'\n    let winner: ChessGameState['winner'] = undefined\n\n    if (game.isGameOver()) {\n      status = 'completed'\n      if (game.isCheckmate()) {\n        winner = game.turn() === 'w' ? 'black' : 'white'\n      } else if (game.isDraw()) {\n        winner = 'draw'\n      }\n    }\n\n    if (status !== gameState.gameStatus || winner !== gameState.winner) {\n      setGameState(prev => ({\n        ...prev,\n        gameStatus: status,\n        winner\n      }))\n    }\n  }, [gameState.game, gameState.gameStatus, gameState.winner])\n\n  return {\n    gameState,\n    selectedSquare,\n    possibleMoves,\n    onSquareClick,\n    onPieceDrop,\n    makeMove,\n    resetGame,\n    updateGameState,\n    isCheck: gameState.game.isCheck(),\n    isCheckmate: gameState.game.isCheckmate(),\n    isDraw: gameState.game.isDraw(),\n    isGameOver: gameState.game.isGameOver(),\n    currentFen: gameState.game.fen(),\n    pgn: gameState.game.pgn()\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;;AAHA;;;AAeO,SAAS,aAAa,UAAmB;;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;iCAAkB,IAAM,CAAC;gBAChE,MAAM,IAAI,sJAAA,CAAA,QAAK,CAAC;gBAChB,aAAa;gBACb,cAAc;gBACd,YAAY;gBACZ,aAAa,EAAE;YACjB,CAAC;;IAED,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAE/D,kCAAkC;IAClC,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,CAAC;YACpC,MAAM,QAAQ,UAAU,IAAI,CAAC,KAAK,CAAC;gBAAE;gBAAQ,SAAS;YAAK;YAC3D,OAAO,MAAM,GAAG;8DAAC,CAAA,OAAQ,KAAK,EAAE;;QAClC;qDAAG;QAAC,UAAU,IAAI;KAAC;IAEnB,cAAc;IACd,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8CAAE,CAAC,MAAc,IAAY;YACtD,sDAAsD;YACtD,+DAA+D;YAE/D,IAAI;gBACF,6CAA6C;gBAC7C,MAAM,WAAW,IAAI,sJAAA,CAAA,QAAK,CAAC,UAAU,IAAI,CAAC,GAAG;gBAC7C,MAAM,OAAO,SAAS,IAAI,CAAC;oBACzB;oBACA;oBACA,WAAW,aAAa,IAAI,6BAA6B;gBAC3D;gBAEA,IAAI,MAAM;oBACR,uCAAuC;oBACvC;8DAAa,CAAA,OAAQ,CAAC;gCACpB,GAAG,IAAI;gCACP,MAAM;gCACN,aAAa;uCAAI,KAAK,WAAW;oCAAE,KAAK,GAAG;iCAAC;gCAC5C,cAAc,KAAK,sDAAsD;4BAC3E,CAAC;;oBAED,OAAO;wBAAE,SAAS;wBAAM;oBAAK;gBAC/B,OAAO;oBACL,OAAO;wBAAE,SAAS;wBAAO,OAAO;oBAAe;gBACjD;YACF,EAAE,OAAO,OAAO;gBACd,OAAO;oBAAE,SAAS;oBAAO,OAAO;gBAAe;YACjD;QACF;6CAAG;QAAC,UAAU,IAAI;KAAC;IAEnB,sBAAsB;IACtB,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE,CAAC;YACjC,iEAAiE;YACjE,IAAI,CAAC,gBAAgB;gBACnB,MAAM,QAAQ,UAAU,IAAI,CAAC,GAAG,CAAC;gBACjC,yDAAyD;gBACzD,IAAI,OAAO;oBACT,kBAAkB;oBAClB,iBAAiB,iBAAiB;gBACpC;gBACA;YACF;YAEA,0CAA0C;YAC1C,IAAI,mBAAmB,QAAQ;gBAC7B,kBAAkB;gBAClB,iBAAiB,EAAE;gBACnB;YACF;YAEA,qBAAqB;YACrB,MAAM,aAAa,SAAS,gBAAgB;YAE5C,6CAA6C;YAC7C,kBAAkB;YAClB,iBAAiB,EAAE;YAEnB,OAAO;QACT;kDAAG;QAAC;QAAgB,UAAU,IAAI;QAAE;QAAkB;KAAS;IAE/D,oCAAoC;IACpC,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE,CAAC,cAAsB;YACrD,MAAM,SAAS,SAAS,cAAc;YACtC,OAAO,QAAQ,WAAW;QAC5B;gDAAG;QAAC;KAAS;IAEb,aAAa;IACb,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+CAAE;YAC5B;uDAAa,CAAA,OAAQ,CAAC;wBACpB,GAAG,IAAI;wBACP,MAAM,IAAI,sJAAA,CAAA,QAAK;wBACf,aAAa,EAAE;wBACf,YAAY;wBACZ,QAAQ;wBACR,cAAc;oBAChB,CAAC;;YACD,kBAAkB;YAClB,iBAAiB,EAAE;QACrB;8CAAG,EAAE;IAEL,oDAAoD;IACpD,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,CAAC;YACnC;6DAAa,CAAA,OAAQ,CAAC;wBACpB,GAAG,IAAI;wBACP,GAAG,QAAQ;wBACX,MAAM,SAAS,IAAI,GAAG,IAAI,sJAAA,CAAA,QAAK,CAAC,SAAS,IAAI,CAAC,GAAG,MAAM,KAAK,IAAI;oBAClE,CAAC;;QACH;oDAAG,EAAE;IAEL,oBAAoB;IACpB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,OAAO,UAAU,IAAI;YAC3B,IAAI,SAAuC;YAC3C,IAAI,SAAmC;YAEvC,IAAI,KAAK,UAAU,IAAI;gBACrB,SAAS;gBACT,IAAI,KAAK,WAAW,IAAI;oBACtB,SAAS,KAAK,IAAI,OAAO,MAAM,UAAU;gBAC3C,OAAO,IAAI,KAAK,MAAM,IAAI;oBACxB,SAAS;gBACX;YACF;YAEA,IAAI,WAAW,UAAU,UAAU,IAAI,WAAW,UAAU,MAAM,EAAE;gBAClE;8CAAa,CAAA,OAAQ,CAAC;4BACpB,GAAG,IAAI;4BACP,YAAY;4BACZ;wBACF,CAAC;;YACH;QACF;iCAAG;QAAC,UAAU,IAAI;QAAE,UAAU,UAAU;QAAE,UAAU,MAAM;KAAC;IAE3D,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,SAAS,UAAU,IAAI,CAAC,OAAO;QAC/B,aAAa,UAAU,IAAI,CAAC,WAAW;QACvC,QAAQ,UAAU,IAAI,CAAC,MAAM;QAC7B,YAAY,UAAU,IAAI,CAAC,UAAU;QACrC,YAAY,UAAU,IAAI,CAAC,GAAG;QAC9B,KAAK,UAAU,IAAI,CAAC,GAAG;IACzB;AACF;GArJgB", "debugId": null}}, {"offset": {"line": 593, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitHub/chess_web/src/components/chess/ChessBoard.tsx"], "sourcesContent": ["'use client'\n\nimport { Chessboard } from 'react-chessboard'\nimport { Square } from 'chess.js'\nimport { useChessGame } from '@/hooks/useChessGame'\nimport { Crown, RotateCcw, Flag } from 'lucide-react'\nimport { useMemo } from 'react'\n\ninterface ChessBoardProps {\n  gameId?: string\n  playerColor?: 'white' | 'black'\n  isSpectator?: boolean\n  onMove?: (move: any) => void\n  initialFen?: string\n}\n\nexport default function ChessBoard({ \n  gameId, \n  playerColor = 'white', \n  isSpectator = false,\n  onMove,\n  initialFen \n}: ChessBoardProps) {\n  const {\n    gameState,\n    selectedSquare,\n    possibleMoves,\n    onSquareClick,\n    onPieceDrop,\n    resetGame,\n    isCheck,\n    isCheckmate,\n    isDraw,\n    isGameOver,\n    currentFen,\n    pgn\n  } = useChessGame(initialFen)\n\n  // Custom square styles for highlighting\n  const customSquareStyles = useMemo(() => {\n    const styles: { [square: string]: React.CSSProperties } = {}\n\n    // Highlight selected square\n    if (selectedSquare) {\n      styles[selectedSquare] = {\n        backgroundColor: 'rgba(255, 255, 0, 0.4)'\n      }\n    }\n\n    // Highlight possible moves\n    possibleMoves.forEach(square => {\n      styles[square] = {\n        background: 'radial-gradient(circle, rgba(0,0,0,.1) 25%, transparent 25%)',\n        borderRadius: '50%'\n      }\n    })\n\n    // Highlight check\n    if (isCheck) {\n      const kingSquare = gameState.game.board().flat().find(\n        piece => piece && piece.type === 'k' && piece.color === gameState.game.turn()\n      )\n      if (kingSquare) {\n        // Find king position - this is a simplified approach\n        for (let rank = 0; rank < 8; rank++) {\n          for (let file = 0; file < 8; file++) {\n            const square = String.fromCharCode(97 + file) + (8 - rank) as Square\n            const piece = gameState.game.get(square)\n            if (piece && piece.type === 'k' && piece.color === gameState.game.turn()) {\n              styles[square] = {\n                backgroundColor: 'rgba(255, 0, 0, 0.4)'\n              }\n            }\n          }\n        }\n      }\n    }\n\n    return styles\n  }, [selectedSquare, possibleMoves, isCheck, gameState.game])\n\n  const handlePieceDrop = ({ sourceSquare, targetSquare }: any) => {\n    if (isSpectator) return false\n\n    const result = onPieceDrop(sourceSquare as Square, targetSquare as Square)\n\n    if (result && onMove) {\n      // Get the last move from the game\n      const history = gameState.game.history({ verbose: true })\n      const lastMove = history[history.length - 1]\n      onMove(lastMove)\n    }\n\n    return result\n  }\n\n  const handleSquareClick = ({ square }: any) => {\n    if (!isSpectator) {\n      onSquareClick(square as Square)\n    }\n  }\n\n  const getGameStatusMessage = () => {\n    if (isCheckmate) {\n      const winner = gameState.game.turn() === 'w' ? 'Black' : 'White'\n      return `Checkmate! ${winner} wins!`\n    }\n    if (isDraw) {\n      return 'Game ended in a draw'\n    }\n    if (isCheck) {\n      return 'Check!'\n    }\n    if (gameState.gameStatus === 'waiting') {\n      return 'Waiting for opponent...'\n    }\n    return `${gameState.game.turn() === 'w' ? 'White' : 'Black'} to move`\n  }\n\n  return (\n    <div className=\"flex flex-col items-center space-y-4\">\n      {/* Game Status */}\n      <div className=\"bg-white rounded-lg shadow-md p-4 w-full max-w-md text-center\">\n        <div className=\"flex items-center justify-center space-x-2 mb-2\">\n          <Crown className=\"h-5 w-5 text-yellow-500\" />\n          <span className=\"font-semibold text-gray-800\">\n            {getGameStatusMessage()}\n          </span>\n        </div>\n        \n        {gameState.gameStatus === 'active' && (\n          <div className=\"text-sm text-gray-600\">\n            Turn: {gameState.game.turn() === 'w' ? 'White' : 'Black'}\n          </div>\n        )}\n      </div>\n\n      {/* Chess Board */}\n      <div className=\"relative\">\n        <Chessboard\n          options={{\n            position: currentFen,\n            onPieceDrop: handlePieceDrop,\n            onSquareClick: !isSpectator ? handleSquareClick : undefined,\n            boardOrientation: playerColor,\n            squareStyles: customSquareStyles,\n            allowDrawingArrows: true,\n            boardStyle: {\n              borderRadius: '8px',\n              boxShadow: '0 10px 25px rgba(0, 0, 0, 0.2)',\n              width: '400px',\n              height: '400px'\n            }\n          }}\n        />\n        \n        {/* Game Over Overlay */}\n        {isGameOver && (\n          <div className=\"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center rounded-lg\">\n            <div className=\"bg-white p-6 rounded-lg text-center\">\n              <h3 className=\"text-xl font-bold mb-2\">Game Over</h3>\n              <p className=\"text-gray-600 mb-4\">{getGameStatusMessage()}</p>\n              {!isSpectator && (\n                <button\n                  onClick={resetGame}\n                  className=\"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700\"\n                >\n                  <RotateCcw className=\"h-4 w-4 mr-2\" />\n                  New Game\n                </button>\n              )}\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Game Controls */}\n      {!isSpectator && gameState.gameStatus === 'active' && (\n        <div className=\"flex space-x-2\">\n          <button\n            onClick={resetGame}\n            className=\"inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50\"\n          >\n            <RotateCcw className=\"h-4 w-4 mr-1\" />\n            Reset\n          </button>\n          \n          <button\n            className=\"inline-flex items-center px-3 py-2 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-white hover:bg-red-50\"\n          >\n            <Flag className=\"h-4 w-4 mr-1\" />\n            Resign\n          </button>\n        </div>\n      )}\n\n      {/* Move History */}\n      <div className=\"bg-white rounded-lg shadow-md p-4 w-full max-w-md\">\n        <h3 className=\"font-semibold text-gray-800 mb-2\">Move History</h3>\n        <div className=\"max-h-32 overflow-y-auto text-sm\">\n          {gameState.moveHistory.length > 0 ? (\n            <div className=\"grid grid-cols-2 gap-2\">\n              {gameState.moveHistory.map((move, index) => (\n                <div key={index} className=\"text-gray-600\">\n                  {Math.floor(index / 2) + 1}.{index % 2 === 0 ? '' : '..'} {move}\n                </div>\n              ))}\n            </div>\n          ) : (\n            <p className=\"text-gray-500\">No moves yet</p>\n          )}\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AAAA;AAAA;AACA;;;AANA;;;;;AAgBe,SAAS,WAAW,EACjC,MAAM,EACN,cAAc,OAAO,EACrB,cAAc,KAAK,EACnB,MAAM,EACN,UAAU,EACM;;IAChB,MAAM,EACJ,SAAS,EACT,cAAc,EACd,aAAa,EACb,aAAa,EACb,WAAW,EACX,SAAS,EACT,OAAO,EACP,WAAW,EACX,MAAM,EACN,UAAU,EACV,UAAU,EACV,GAAG,EACJ,GAAG,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD,EAAE;IAEjB,wCAAwC;IACxC,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;kDAAE;YACjC,MAAM,SAAoD,CAAC;YAE3D,4BAA4B;YAC5B,IAAI,gBAAgB;gBAClB,MAAM,CAAC,eAAe,GAAG;oBACvB,iBAAiB;gBACnB;YACF;YAEA,2BAA2B;YAC3B,cAAc,OAAO;0DAAC,CAAA;oBACpB,MAAM,CAAC,OAAO,GAAG;wBACf,YAAY;wBACZ,cAAc;oBAChB;gBACF;;YAEA,kBAAkB;YAClB,IAAI,SAAS;gBACX,MAAM,aAAa,UAAU,IAAI,CAAC,KAAK,GAAG,IAAI,GAAG,IAAI;yEACnD,CAAA,QAAS,SAAS,MAAM,IAAI,KAAK,OAAO,MAAM,KAAK,KAAK,UAAU,IAAI,CAAC,IAAI;;gBAE7E,IAAI,YAAY;oBACd,qDAAqD;oBACrD,IAAK,IAAI,OAAO,GAAG,OAAO,GAAG,OAAQ;wBACnC,IAAK,IAAI,OAAO,GAAG,OAAO,GAAG,OAAQ;4BACnC,MAAM,SAAS,OAAO,YAAY,CAAC,KAAK,QAAQ,CAAC,IAAI,IAAI;4BACzD,MAAM,QAAQ,UAAU,IAAI,CAAC,GAAG,CAAC;4BACjC,IAAI,SAAS,MAAM,IAAI,KAAK,OAAO,MAAM,KAAK,KAAK,UAAU,IAAI,CAAC,IAAI,IAAI;gCACxE,MAAM,CAAC,OAAO,GAAG;oCACf,iBAAiB;gCACnB;4BACF;wBACF;oBACF;gBACF;YACF;YAEA,OAAO;QACT;iDAAG;QAAC;QAAgB;QAAe;QAAS,UAAU,IAAI;KAAC;IAE3D,MAAM,kBAAkB,CAAC,EAAE,YAAY,EAAE,YAAY,EAAO;QAC1D,IAAI,aAAa,OAAO;QAExB,MAAM,SAAS,YAAY,cAAwB;QAEnD,IAAI,UAAU,QAAQ;YACpB,kCAAkC;YAClC,MAAM,UAAU,UAAU,IAAI,CAAC,OAAO,CAAC;gBAAE,SAAS;YAAK;YACvD,MAAM,WAAW,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE;YAC5C,OAAO;QACT;QAEA,OAAO;IACT;IAEA,MAAM,oBAAoB,CAAC,EAAE,MAAM,EAAO;QACxC,IAAI,CAAC,aAAa;YAChB,cAAc;QAChB;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI,aAAa;YACf,MAAM,SAAS,UAAU,IAAI,CAAC,IAAI,OAAO,MAAM,UAAU;YACzD,OAAO,CAAC,WAAW,EAAE,OAAO,MAAM,CAAC;QACrC;QACA,IAAI,QAAQ;YACV,OAAO;QACT;QACA,IAAI,SAAS;YACX,OAAO;QACT;QACA,IAAI,UAAU,UAAU,KAAK,WAAW;YACtC,OAAO;QACT;QACA,OAAO,GAAG,UAAU,IAAI,CAAC,IAAI,OAAO,MAAM,UAAU,QAAQ,QAAQ,CAAC;IACvE;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAK,WAAU;0CACb;;;;;;;;;;;;oBAIJ,UAAU,UAAU,KAAK,0BACxB,6LAAC;wBAAI,WAAU;;4BAAwB;4BAC9B,UAAU,IAAI,CAAC,IAAI,OAAO,MAAM,UAAU;;;;;;;;;;;;;0BAMvD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,8JAAA,CAAA,aAAU;wBACT,SAAS;4BACP,UAAU;4BACV,aAAa;4BACb,eAAe,CAAC,cAAc,oBAAoB;4BAClD,kBAAkB;4BAClB,cAAc;4BACd,oBAAoB;4BACpB,YAAY;gCACV,cAAc;gCACd,WAAW;gCACX,OAAO;gCACP,QAAQ;4BACV;wBACF;;;;;;oBAID,4BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAyB;;;;;;8CACvC,6LAAC;oCAAE,WAAU;8CAAsB;;;;;;gCAClC,CAAC,6BACA,6LAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;YAUjD,CAAC,eAAe,UAAU,UAAU,KAAK,0BACxC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS;wBACT,WAAU;;0CAEV,6LAAC,mNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;kCAIxC,6LAAC;wBACC,WAAU;;0CAEV,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAOvC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,6LAAC;wBAAI,WAAU;kCACZ,UAAU,WAAW,CAAC,MAAM,GAAG,kBAC9B,6LAAC;4BAAI,WAAU;sCACZ,UAAU,WAAW,CAAC,GAAG,CAAC,CAAC,MAAM,sBAChC,6LAAC;oCAAgB,WAAU;;wCACxB,KAAK,KAAK,CAAC,QAAQ,KAAK;wCAAE;wCAAE,QAAQ,MAAM,IAAI,KAAK;wCAAK;wCAAE;;mCADnD;;;;;;;;;iDAMd,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;;;;;;;AAMzC;GAvMwB;;QAoBlB,+HAAA,CAAA,eAAY;;;KApBM", "debugId": null}}, {"offset": {"line": 940, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitHub/chess_web/src/app/demo/page.tsx"], "sourcesContent": ["'use client'\n\nimport Navigation from '@/components/layout/Navigation'\nimport ChessBoard from '@/components/chess/ChessBoard'\nimport { ArrowLeft } from 'lucide-react'\nimport Link from 'next/link'\n\nexport default function DemoPage() {\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Navigation />\n      \n      <div className=\"max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8\">\n        <div className=\"mb-8\">\n          <Link\n            href=\"/\"\n            className=\"inline-flex items-center text-sm text-gray-500 hover:text-gray-700 mb-4\"\n          >\n            <ArrowLeft className=\"h-4 w-4 mr-1\" />\n            Back to Home\n          </Link>\n          <h1 className=\"text-3xl font-extrabold text-gray-900 mb-2\">\n            Chess Demo\n          </h1>\n          <p className=\"text-lg text-gray-600\">\n            Try out the chess board! You can play against yourself to test the functionality.\n          </p>\n        </div>\n\n        <div className=\"flex justify-center\">\n          <ChessBoard \n            playerColor=\"white\"\n            isSpectator={false}\n          />\n        </div>\n\n        <div className=\"mt-8 max-w-2xl mx-auto\">\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">How to Play</h2>\n            <ul className=\"space-y-2 text-gray-600\">\n              <li>• Click on a piece to select it</li>\n              <li>• Click on a highlighted square to move the piece</li>\n              <li>• You can also drag and drop pieces</li>\n              <li>• The game follows standard chess rules</li>\n              <li>• Move history is shown below the board</li>\n            </ul>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,6IAAA,CAAA,UAAU;;;;;0BAEX,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGxC,6LAAC;gCAAG,WAAU;0CAA6C;;;;;;0CAG3D,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAKvC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,4IAAA,CAAA,UAAU;4BACT,aAAY;4BACZ,aAAa;;;;;;;;;;;kCAIjB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA2C;;;;;;8CACzD,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlB;KA5CwB", "debugId": null}}]}