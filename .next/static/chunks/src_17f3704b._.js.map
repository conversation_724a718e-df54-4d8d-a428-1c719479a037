{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitHub/chess_web/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createBrowserClient } from '@supabase/ssr'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY\n\n// Check if Supabase is configured\nconst isSupabaseConfigured = supabaseUrl && supabaseAnonKey &&\n  supabaseUrl !== 'your_supabase_project_url' &&\n  supabaseAnonKey !== 'your_supabase_anon_key'\n\n// Client-side Supabase client\nexport const supabase = isSupabaseConfigured\n  ? createBrowserClient(supabaseUrl!, supabaseAnonKey!)\n  : null\n\n// Server-side Supabase client (for API routes)\nexport const createServerClient = () => {\n  if (!isSupabaseConfigured) {\n    throw new Error('Supabase is not configured. Please set NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY environment variables.')\n  }\n  return createClient(supabaseUrl!, supabaseAnonKey!)\n}\n\n// Database types\nexport interface User {\n  id: string\n  email: string\n  display_name: string\n  avatar_url?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface Game {\n  id: string\n  white_player_id: string\n  black_player_id?: string\n  game_state: string // FEN notation\n  moves: string[] // Array of moves in algebraic notation\n  status: 'waiting' | 'active' | 'completed' | 'abandoned'\n  winner?: 'white' | 'black' | 'draw'\n  created_at: string\n  updated_at: string\n}\n\nexport interface GameMove {\n  id: string\n  game_id: string\n  player_id: string\n  move: string\n  fen_after: string\n  created_at: string\n}\n\nexport interface ChatMessage {\n  id: string\n  game_id: string\n  user_id: string\n  message: string\n  created_at: string\n}\n"], "names": [], "mappings": ";;;;AAGoB;AAHpB;AACA;AAAA;;;AAEA,MAAM;AACN,MAAM;AAEN,kCAAkC;AAClC,MAAM,uBAAuB,eAAe,mBAC1C,gBAAgB,+BAChB,oBAAoB;AAGf,MAAM,WAAW,uCACpB,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD,EAAE,aAAc;AAI/B,MAAM,qBAAqB;IAChC,uCAA2B;;IAE3B;IACA,OAAO,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAc;AACpC", "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitHub/chess_web/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useEffect, useState } from 'react'\nimport { User as SupabaseUser } from '@supabase/supabase-js'\nimport { supabase, User } from '@/lib/supabase'\n\ninterface AuthContextType {\n  user: User | null\n  supabaseUser: SupabaseUser | null\n  loading: boolean\n  signInWithGoogle: () => Promise<void>\n  signOut: () => Promise<void>\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [supabaseUser, setSupabaseUser] = useState<SupabaseUser | null>(null)\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    // Check if Supabase is configured\n    if (!supabase) {\n      setLoading(false)\n      return\n    }\n\n    // Get initial session\n    const getInitialSession = async () => {\n      const { data: { session } } = await supabase.auth.getSession()\n      if (session?.user) {\n        setSupabaseUser(session.user)\n        await fetchUserProfile(session.user.id)\n      }\n      setLoading(false)\n    }\n\n    getInitialSession()\n\n    // Listen for auth changes\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        if (session?.user) {\n          setSupabaseUser(session.user)\n          await fetchUserProfile(session.user.id)\n        } else {\n          setSupabaseUser(null)\n          setUser(null)\n        }\n        setLoading(false)\n      }\n    )\n\n    return () => subscription.unsubscribe()\n  }, [])\n\n  const fetchUserProfile = async (userId: string) => {\n    if (!supabase) return\n\n    try {\n      const { data, error } = await supabase\n        .from('users')\n        .select('*')\n        .eq('id', userId)\n        .single()\n\n      if (error) {\n        console.error('Error fetching user profile:', error)\n        return\n      }\n\n      setUser(data)\n    } catch (error) {\n      console.error('Error fetching user profile:', error)\n    }\n  }\n\n  const signInWithGoogle = async () => {\n    try {\n      // Check if Supabase is properly configured\n      if (!supabase) {\n        alert('Please configure Supabase environment variables. See README.md for setup instructions.')\n        return\n      }\n\n      const { error } = await supabase.auth.signInWithOAuth({\n        provider: 'google',\n        options: {\n          redirectTo: `${window.location.origin}/auth/callback`\n        }\n      })\n      if (error) throw error\n    } catch (error) {\n      console.error('Error signing in with Google:', error)\n      throw error\n    }\n  }\n\n  const signOut = async () => {\n    try {\n      if (!supabase) return\n\n      const { error } = await supabase.auth.signOut()\n      if (error) throw error\n      setUser(null)\n      setSupabaseUser(null)\n    } catch (error) {\n      console.error('Error signing out:', error)\n      throw error\n    }\n  }\n\n  const value = {\n    user,\n    supabaseUser,\n    loading,\n    signInWithGoogle,\n    signOut\n  }\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  )\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;;;AAJA;;;AAcA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IACtE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,kCAAkC;YAClC,IAAI,CAAC,yHAAA,CAAA,WAAQ,EAAE;gBACb,WAAW;gBACX;YACF;YAEA,sBAAsB;YACtB,MAAM;4DAAoB;oBACxB,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU;oBAC5D,IAAI,SAAS,MAAM;wBACjB,gBAAgB,QAAQ,IAAI;wBAC5B,MAAM,iBAAiB,QAAQ,IAAI,CAAC,EAAE;oBACxC;oBACA,WAAW;gBACb;;YAEA;YAEA,0BAA0B;YAC1B,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,iBAAiB;0CAChE,OAAO,OAAO;oBACZ,IAAI,SAAS,MAAM;wBACjB,gBAAgB,QAAQ,IAAI;wBAC5B,MAAM,iBAAiB,QAAQ,IAAI,CAAC,EAAE;oBACxC,OAAO;wBACL,gBAAgB;wBAChB,QAAQ;oBACV;oBACA,WAAW;gBACb;;YAGF;0CAAO,IAAM,aAAa,WAAW;;QACvC;iCAAG,EAAE;IAEL,MAAM,mBAAmB,OAAO;QAC9B,IAAI,CAAC,yHAAA,CAAA,WAAQ,EAAE;QAEf,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QACT,MAAM;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,gCAAgC;gBAC9C;YACF;YAEA,QAAQ;QACV,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI;YACF,2CAA2C;YAC3C,IAAI,CAAC,yHAAA,CAAA,WAAQ,EAAE;gBACb,MAAM;gBACN;YACF;YAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,eAAe,CAAC;gBACpD,UAAU;gBACV,SAAS;oBACP,YAAY,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,cAAc,CAAC;gBACvD;YACF;YACA,IAAI,OAAO,MAAM;QACnB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;IAEA,MAAM,UAAU;QACd,IAAI;YACF,IAAI,CAAC,yHAAA,CAAA,WAAQ,EAAE;YAEf,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;YAC7C,IAAI,OAAO,MAAM;YACjB,QAAQ;YACR,gBAAgB;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;YACpC,MAAM;QACR;IACF;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;GA9GgB;KAAA;AAgHT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}]}