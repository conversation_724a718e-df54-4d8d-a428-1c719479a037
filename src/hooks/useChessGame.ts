'use client'

import { useState, useCallback, useEffect } from 'react'
import { Chess } from 'chess.js'
import { Square } from 'react-chessboard/dist/chessboard/types'

export interface ChessGameState {
  game: Chess
  gameId?: string
  playerColor: 'white' | 'black'
  isPlayerTurn: boolean
  gameStatus: 'waiting' | 'active' | 'completed' | 'abandoned'
  winner?: 'white' | 'black' | 'draw'
  moveHistory: string[]
}

export function useChessGame(initialFen?: string) {
  const [gameState, setGameState] = useState<ChessGameState>(() => ({
    game: new Chess(initialFen),
    playerColor: 'white',
    isPlayerTurn: true,
    gameStatus: 'waiting',
    moveHistory: []
  }))

  const [selectedSquare, setSelectedSquare] = useState<Square | null>(null)
  const [possibleMoves, setPossibleMoves] = useState<Square[]>([])

  // Get possible moves for a square
  const getPossibleMoves = useCallback((square: Square): Square[] => {
    const moves = gameState.game.moves({ square, verbose: true })
    return moves.map(move => move.to as Square)
  }, [gameState.game])

  // Handle square click
  const onSquareClick = useCallback((square: Square) => {
    // If no square is selected, select this square if it has a piece
    if (!selectedSquare) {
      const piece = gameState.game.get(square)
      if (piece && piece.color === (gameState.playerColor === 'white' ? 'w' : 'b')) {
        setSelectedSquare(square)
        setPossibleMoves(getPossibleMoves(square))
      }
      return
    }

    // If the same square is clicked, deselect
    if (selectedSquare === square) {
      setSelectedSquare(null)
      setPossibleMoves([])
      return
    }

    // Try to make a move
    const moveResult = makeMove(selectedSquare, square)
    
    // Clear selection regardless of move success
    setSelectedSquare(null)
    setPossibleMoves([])

    return moveResult
  }, [selectedSquare, gameState, getPossibleMoves])

  // Make a move
  const makeMove = useCallback((from: Square, to: Square, promotion?: string) => {
    if (!gameState.isPlayerTurn) {
      return { success: false, error: 'Not your turn' }
    }

    try {
      const move = gameState.game.move({
        from,
        to,
        promotion: promotion || 'q' // Default to queen promotion
      })

      if (move) {
        setGameState(prev => ({
          ...prev,
          game: new Chess(prev.game.fen()),
          moveHistory: [...prev.moveHistory, move.san],
          isPlayerTurn: false // Will be updated by real-time sync
        }))

        return { success: true, move }
      } else {
        return { success: false, error: 'Invalid move' }
      }
    } catch (error) {
      return { success: false, error: 'Invalid move' }
    }
  }, [gameState])

  // Handle piece drop (drag and drop)
  const onPieceDrop = useCallback((sourceSquare: Square, targetSquare: Square) => {
    const result = makeMove(sourceSquare, targetSquare)
    return result?.success || false
  }, [makeMove])

  // Reset game
  const resetGame = useCallback(() => {
    setGameState(prev => ({
      ...prev,
      game: new Chess(),
      moveHistory: [],
      gameStatus: 'waiting',
      winner: undefined
    }))
    setSelectedSquare(null)
    setPossibleMoves([])
  }, [])

  // Update game from external state (for multiplayer)
  const updateGameState = useCallback((newState: Partial<ChessGameState>) => {
    setGameState(prev => ({
      ...prev,
      ...newState,
      game: newState.game ? new Chess(newState.game.fen()) : prev.game
    }))
  }, [])

  // Check game status
  useEffect(() => {
    const game = gameState.game
    let status: ChessGameState['gameStatus'] = 'active'
    let winner: ChessGameState['winner'] = undefined

    if (game.isGameOver()) {
      status = 'completed'
      if (game.isCheckmate()) {
        winner = game.turn() === 'w' ? 'black' : 'white'
      } else if (game.isDraw()) {
        winner = 'draw'
      }
    }

    if (status !== gameState.gameStatus || winner !== gameState.winner) {
      setGameState(prev => ({
        ...prev,
        gameStatus: status,
        winner
      }))
    }
  }, [gameState.game, gameState.gameStatus, gameState.winner])

  return {
    gameState,
    selectedSquare,
    possibleMoves,
    onSquareClick,
    onPieceDrop,
    makeMove,
    resetGame,
    updateGameState,
    isCheck: gameState.game.isCheck(),
    isCheckmate: gameState.game.isCheckmate(),
    isDraw: gameState.game.isDraw(),
    isGameOver: gameState.game.isGameOver(),
    currentFen: gameState.game.fen(),
    pgn: gameState.game.pgn()
  }
}
